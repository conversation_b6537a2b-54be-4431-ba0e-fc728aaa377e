<?php

/**
 * Instagram
 */

// Get curved edge settings from page builder
$curved_edge = get_sub_field('curve');
$flip_curved_edge = get_sub_field('flip_curve');
$background_colour = get_sub_field('background_colour');

// Get social links from options (these are global)
$social_links = get_field('social_links', 'options');

// Build CSS classes for the inner container
$inner_classes = ['instagram__inner'];
if ($curved_edge === 'top') {
    $inner_classes[] = 'curve-top';
    if ($flip_curved_edge) {
        $inner_classes[] = 'curve-flipped';
    }
} elseif ($curved_edge === 'bottom') {
    $inner_classes[] = 'curve-bottom';
    if ($flip_curved_edge) {
        $inner_classes[] = 'curve-flipped';
    }
} elseif ($curved_edge === 'none') {
    $inner_classes[] = 'curve-none';
}

// Build background style
$background_style = '';
if (!empty($background_colour)) {
    $background_style = ' style="background-color: ' . esc_attr($background_colour) . ';"';
}

?>

<?php if(have_rows('instagram_block', 'options')) : ?>
    <?php while(have_rows('instagram_block', 'options')) : the_row(); ?>
        <?php

        $heading = get_sub_field('heading');
        $copy = get_sub_field('copy');
        $links_heading = get_sub_field('links_heading');

        ?>
        <section class="instagram">
            <div class="<?php echo implode(' ', $inner_classes); ?>"<?php echo $background_style; ?> data-aos="fade">
                <?php // Debug: echo '<!-- Classes: ' . implode(' ', $inner_classes) . ' -->'; ?>
                <div class="container instagram__container">
                    <div class="instagram__content inner-container centre">
                        <?php if($heading) : ?>
                            <h2 class="instagram__heading h2-large heading-light heading-underlined text-weight-regular"><?php echo $heading; ?></h2>
                        <?php endif; ?>
                        <?php if($copy) : ?>
                            <div class="instagram__copy content-area">
                                <?php echo $copy; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="container-fluid instagram__container instagram__container--photos">
<!--                    --><?php //if($photos) : ?>
<!--                    <div class="instagram__photos-wrapper">-->
<!--                        <div class="instagram__photos">-->
<!--                            --><?php //foreach($photos as $photo) : ?>
<!--                                --><?php //
//
//                                $thumbnail = get_the_post_thumbnail($photo->ID, 'gram');
//
//                                ?>
<!--                                --><?php //if($thumbnail) : ?>
<!--                                    <div class="instagram__photo">-->
<!--                                        --><?php //echo $thumbnail; ?>
<!--                                    </div>-->
<!--                                --><?php //endif; ?>
<!--                            --><?php //endforeach; ?>
<!--                        </div>-->
<!--                        <span class="instagram__button instagram__button--prev flickity-icon flickity-icon--prev"><i class="fal fa-chevron-double-left"></i></span>-->
<!--                        <span class="instagram__button instagram__button--next flickity-icon flickity-icon--next"><i class="fal fa-chevron-double-right"></i></span>-->
<!--                    </div>-->
<!--                    --><?php //endif; ?>
                    <?php echo do_shortcode('[instagram-feed]'); ?>
                    <br>
                </div>
                <div class="container instagram__container">
                    <div class="instagram__content centre">
                        <?php if($links_heading) : ?>
                            <h4 class="instagram__heading"><?php echo $links_heading; ?></h4>
                        <?php endif; ?>
                        <?php if($social_links) : ?>
                            <div class="instagram__social-links font-zero">
                                <?php foreach($social_links as $social_link) : ?>
                                    <?php

                                    $icon = $social_link['icon'];
                                    $link = $social_link['link'];

                                    ?>

                                    <?php if($link && $link['title']) : ?>
                                        <div class="instagram__link-wrapper inline-block vmiddle">
                                            <a href="<?php echo $link['url']; ?>" <?php echo($link['target']) ? 'target="_blank"' : ''; ?> class="instagram__link social-button"><?php if($icon) : ?><span class="social-button__icon"><img src="<?php echo $icon; ?>" alt="Social Icon"></span><?php endif; ?><span class="social-button__text"><?php echo $link['title']; ?></span></a>
                                        </div>

                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </section><!-- .instagram -->
    <?php endwhile; ?>
<?php endif; ?>
