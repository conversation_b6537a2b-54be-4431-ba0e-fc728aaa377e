.steps-ahead {

    // Hero section styles
    &__hero {
        position: relative;
        overflow: hidden;
    }

    &__hero-image {
        position: relative;

        img {
            width: 100%;
            height: auto;
            display: block;
        }
    }

    &__hero-content {
        position: absolute;
        bottom: 40px;
        left: 50%;
        transform: translateX(-50%);
        text-align: center;
        z-index: 2;
        width: 100%;
        padding: 0 20px;
    }

    &__hero-title {
        color: white;
        font-size: 3.5rem;
        font-weight: 600;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        margin: 0;
        line-height: 1.2;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            font-size: 2.5rem;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            font-size: 2rem;
        }
    }

    &__inner {
        padding: 50px 0;
        position: relative;

        // Background color support
        &.has-background {
            // Default padding for background
            padding: 60px 0;
        }

        // Curved edge support
        &.curve-top {
            padding: 100px 0 40px 0;

            &::before {
                content: '';
                display: block;
                position: absolute;
                top: -2px;
                left: 0;
                width: 100%;
                height: 60px;
                background-image: url('../img/banner-mask.svg');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                background-position: top center;
                transform: scaleY(-1);
                z-index: 1;
            }

            // Flipped version
            &.curve-flipped::before {
                transform: scaleY(-1) scaleX(-1);
            }
        }

        &.curve-bottom {
            padding: 40px 0 100px 0;

            &::after {
                content: '';
                display: block;
                position: absolute;
                bottom: -2px;
                left: 0;
                width: 100%;
                height: 60px;
                background-image: url('../img/banner-mask.svg');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                background-position: bottom center;
                transform: none;
                z-index: 1;
            }

            // Flipped version
            &.curve-flipped::after {
                transform: scaleX(-1);
            }
        }

        // Combined background and curve adjustments
        &.has-background.curve-top {
            padding: 120px 0 60px 0;
        }

        &.has-background.curve-bottom {
            padding: 60px 0 120px 0;
        }
    }

    &__row {
        padding-top: 60px;

        // Use CSS Grid for equal height columns on desktop
        @media only screen and (min-width: map-get($grid-breakpoints, md)) {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 30px;
            align-items: stretch;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, xl)) {
            padding-top: 45px;
        }
    }

    &__col {
        // Remove Bootstrap column styles on desktop when using CSS Grid
        @media only screen and (min-width: map-get($grid-breakpoints, md)) {
            flex: none;
            width: auto;
            max-width: none;
            padding-left: 0;
            padding-right: 0;
        }
    }

    &__col-content {
        padding: 0 0 10px 0;
        margin-bottom: 30px;
        text-align: center;

        // Make columns stretch to full height on desktop
        @media only screen and (min-width: map-get($grid-breakpoints, md)) {
            display: flex;
            flex-direction: column;
            height: 100%;
            margin-bottom: 0;
            padding: 0;
        }

    }

    &__col-image {
        position: relative;
        min-height: 210px;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
            width: 100%;
            height: 210px;
            object-fit: cover;
            display: block;
        }

        // Add gradient overlay for text readability
        &::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60%;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
            z-index: 1;
            pointer-events: none;
        }
    }

    &__col-heading {
        position: absolute;
        bottom: 10px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 2;
        color: white;
        font-size: 24px;
        font-weight: 600;
        margin: 0;
        line-height: 30px;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
        text-align: center;
        width: 100%;
        padding: 0 10px;
        box-sizing: border-box;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            font-size: 20px;
            line-height: 26px;
            bottom: 8px;
        }
    }

    &__col-points {
        text-align: center;
        margin: 0 10px 20px 10px;

        // Expand to fill remaining space on desktop
        @media only screen and (min-width: map-get($grid-breakpoints, md)) {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            padding: 20px 10px 0 10px;
        }
    }

    &__point {
        position: relative;

        &:not(:last-child) {
            &::after {
                content: '';
                display: block;
                width: 80%;
                height: 1px;
                background-color: #ccc;
                margin: 15px auto;
            }
        }

        &:last-child {
            margin-bottom: 0;
        }
    }

    &__point-title {
        color: #333;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 5px;
        line-height: 22px;
        text-align: center;
    }

    &__point-text {
        color: #666;
        font-size: 16px;
        line-height: 20px;
        margin-bottom: 0;
        text-align: center;
    }

    &__col-copy {
        color: #666;
        font-size: 1rem;
        line-height: 1.6;

        p {
            margin-bottom: 0;
        }
    }

    // Legacy support for old image-based columns
    &__image {
        padding-bottom: 20px;
    }

    &__heading {
        margin-bottom: 10px;
    }

    // Listing page specific styles
    &--listing {
        .steps-ahead__inner {
            // Default padding for listing pages
            padding: 80px 0 40px;

            &.curve-bottom {
                padding: 80px 0 100px 0;
            }
        }
    }
}