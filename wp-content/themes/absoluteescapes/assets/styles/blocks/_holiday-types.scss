.holiday-types {
    a {
        font-weight: normal;
        text-decoration: none;

        &:hover,
        &:focus {
            text-decoration: none;

            .holiday-types__image {
                &:before {
                    background: rgba($black, 0.2);
                }
            }

            .holiday-types__heading {
                // Keep white color when in overlay
                .holiday-types__image-overlay & {
                    color: $white;
                }
            }

            .link {
                color: $blue;
            }
        }
    }

    &__inner {
        padding: 60px 0;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            padding: 45px 0;
        }
    }

    &__row {
        display: flex;
        flex-wrap: wrap;
        align-items: stretch;
        gap: 50px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            gap: 30px;
        }

        // Single column layout on mobile/tablet (up to 991px)
        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            flex-direction: column;
        }

        // Use CSS Grid from desktop width for equal height images
        @media only screen and (min-width: map-get($grid-breakpoints, lg)) {
            display: grid;
            grid-template-columns: 1fr 1fr;
            align-items: stretch;
        }
    }

    &__content {
        padding: 15px 0;
        height: 100%;
        display: flex;
        flex-direction: column;

        // Tablet padding
        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) and (min-width: map-get($grid-breakpoints, sm)) {
            padding: 30px 0 15px 0;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            padding: 30px 0 45px;
        }
    }

    &__col {
        position: relative;
        flex: 1;

        &:after {
            content: "";
            display: block;
            position: absolute;
            bottom: 0;
            width: 100%;
            border-bottom: 1px solid $midlightgrey;

            @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                width: calc(108% + 5px);
                left: calc(-4% - 5px);
            }
        }

        .holiday-types__content-col {
            flex: 0 0 50%;
            max-width: 50%;
            display: flex;
            flex-direction: column;

            &:last-child {
                padding-left: 30px;

                @media only screen and (max-width: map-get($grid-breakpoints-max, xl)) {
                    padding-left: 20px;
                }

                @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                    padding-left: 0;
                    padding-top: 20px;
                }
            }
        }

        &:first-child,
        &:nth-child(2) {
            @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                .holiday-types__content-col {
                    flex: 0 0 100%;
                    max-width: 100%;

                    &:nth-child(1) {
                        order: 1;
                    }

                    &:nth-child(2) {
                        order: 2;
                    }

                    .holiday-types__image {
                        text-align: left;
                    }
                }
            }
        }

        &:last-child,
        &:nth-last-child(2) {
            @media only screen and (min-width: map-get($grid-breakpoints, md)) {
                &:after {
                    display: none;
                }
            }
        }


    }

    &__content-row {
        align-items: stretch;
        height: 100%;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            align-items: flex-start;
        }

        // On tablet, make image and text columns the same height
        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) and (min-width: map-get($grid-breakpoints, sm)) {
            align-items: stretch;
        }
    }

    &__heading {
        transition: 300ms;

        // Mobile font size for all headings
        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            font-size: 24px;
        }

        // When inside image overlay
        .holiday-types__image-overlay & {
            color: $white;
            margin: 0;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);

            @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                font-size: 1.5rem;
            }

            @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                font-size: 24px;
            }
        }
    }

    &__image-wrapper {
        position: relative;
        overflow: hidden; // Contain the image overlay within wrapper bounds
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    &__image {
        position: relative;
        text-align: left;
        display: flex;
        flex: 1;
        flex-direction: column;

        // From desktop width, ensure image containers stretch to match content height
        @media only screen and (min-width: map-get($grid-breakpoints, lg)) {
            height: 100%;
            align-self: stretch;
        }

        img {
            object-fit: cover;
            width: 100%;
            height: 100%;
            min-height: 250px;
            display: block;
            position: relative;

            // From desktop width, ensure images fill available height and match tallest content
            @media only screen and (min-width: map-get($grid-breakpoints, lg)) {
                height: 100%;
                min-height: 100%;
                object-fit: cover;
            }

            // On tablet, keep consistent height for all images
            @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) and (min-width: map-get($grid-breakpoints, sm)) {
                height: 250px;
            }
        }

        // Create overlay that matches the image dimensions exactly
        &:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba($black, 0);
            transition: 300ms;
            z-index: 3;
            pointer-events: none;
        }
    }

    &__image-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 2;
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
        padding: 40px 20px 20px;
        pointer-events: none; // Allow clicks to pass through to the link
        display: flex;
        justify-content: space-between;
        align-items: flex-end;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            padding: 30px 15px 15px;
        }
    }

    &__icon-wrapper {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
    }

    &__icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #3e5056;

        img {
            width: 20px;
            height: 20px;
            filter: brightness(0) invert(1);
        }

        svg {
            width: 22px;
            height: 22px;
            fill: white;
            color: white;
        }

        i {
            font-size: 18px;
            color: white;
        }
    }



    &__text {
        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            padding: 8px 0;
        }
    }

    &__copy {
        p,
        li {
            color: $bluegrey;
        }
    }


}
